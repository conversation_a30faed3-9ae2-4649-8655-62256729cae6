package pox.com.dianfeng.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.dto.StatisticsDTO;
import pox.com.dianfeng.service.IStatisticsService;

import java.util.List;
import java.util.Map;

/**
 * 统计数据控制器
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Api(tags = "统计数据管理")
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    @Autowired
    private IStatisticsService statisticsService;

    /**
     * 获取总用户数
     */
    @ApiOperation("获取总用户数")
    @GetMapping("/total-users")
    public R<Long> getTotalUsers() {
        try {
            Long totalUsers = statisticsService.getTotalUsers();
            return R.ok(totalUsers);
        } catch (Exception e) {
            log.error("获取总用户数失败", e);
            return R.error("获取总用户数失败");
        }
    }

    /**
     * 获取总课程数
     */
    @ApiOperation("获取总课程数")
    @GetMapping("/total-courses")
    public R<Long> getTotalCourses() {
        try {
            Long totalCourses = statisticsService.getTotalCourses();
            return R.ok(totalCourses);
        } catch (Exception e) {
            log.error("获取总课程数失败", e);
            return R.error("获取总课程数失败");
        }
    }

    /**
     * 获取今日新增用户数
     */
    @ApiOperation("获取今日新增用户数")
    @GetMapping("/today-new-users")
    public R<Long> getTodayNewUsers() {
        try {
            Long todayNewUsers = statisticsService.getTodayNewUsers();
            return R.ok(todayNewUsers);
        } catch (Exception e) {
            log.error("获取今日新增用户数失败", e);
            return R.error("获取今日新增用户数失败");
        }
    }

    /**
     * 获取今日销售额
     */
    @ApiOperation("获取今日销售额")
    @GetMapping("/today-sales")
    public R<Double> getTodaySales() {
        try {
            Double todaySales = statisticsService.getTodaySales();
            return R.ok(todaySales);
        } catch (Exception e) {
            log.error("获取今日销售额失败", e);
            return R.error("获取今日销售额失败");
        }
    }

    /**
     * 获取用户增长趋势数据
     */
    @ApiOperation("获取用户增长趋势数据")
    @GetMapping("/user-growth-trend")
    public R<List<StatisticsDTO.TrendData>> getUserGrowthTrend(
            @ApiParam(value = "天数", defaultValue = "30") @RequestParam(defaultValue = "30") Integer days) {
        try {
            List<StatisticsDTO.TrendData> trendData = statisticsService.getUserGrowthTrend(days);
            return R.ok(trendData);
        } catch (Exception e) {
            log.error("获取用户增长趋势数据失败", e);
            return R.error("获取用户增长趋势数据失败");
        }
    }

    /**
     * 获取销售额趋势数据
     */
    @ApiOperation("获取销售额趋势数据")
    @GetMapping("/sales-trend")
    public R<List<StatisticsDTO.SalesTrendData>> getSalesTrend(
            @ApiParam(value = "天数", defaultValue = "30") @RequestParam(defaultValue = "30") Integer days) {
        try {
            List<StatisticsDTO.SalesTrendData> salesTrend = statisticsService.getSalesTrend(days);
            return R.ok(salesTrend);
        } catch (Exception e) {
            log.error("获取销售额趋势数据失败", e);
            return R.error("获取销售额趋势数据失败");
        }
    }

    /**
     * 获取课程分类分布数据
     */
    @ApiOperation("获取课程分类分布数据")
    @GetMapping("/courses-category-distribution")
    public R<List<StatisticsDTO.CategoryDistribution>> getCoursesCategoryDistribution() {
        try {
            List<StatisticsDTO.CategoryDistribution> distribution = 
                    statisticsService.getCoursesCategoryDistribution();
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取课程分类分布数据失败", e);
            return R.error("获取课程分类分布数据失败");
        }
    }

    /**
     * 获取热门课程排行
     */
    @ApiOperation("获取热门课程排行")
    @GetMapping("/hot-courses")
    public R<List<StatisticsDTO.HotCourse>> getHotCourses(
            @ApiParam(value = "限制数量", defaultValue = "5") @RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<StatisticsDTO.HotCourse> hotCourses = statisticsService.getHotCourses(limit);
            return R.ok(hotCourses);
        } catch (Exception e) {
            log.error("获取热门课程排行失败", e);
            return R.error("获取热门课程排行失败");
        }
    }

    /**
     * 获取活跃用户数
     */
    @ApiOperation("获取活跃用户数")
    @GetMapping("/active-users")
    public R<Long> getActiveUsers() {
        try {
            Long activeUsers = statisticsService.getActiveUsers();
            return R.ok(activeUsers);
        } catch (Exception e) {
            log.error("获取活跃用户数失败", e);
            return R.error("获取活跃用户数失败");
        }
    }

    /**
     * 获取课程完成率统计
     */
    @ApiOperation("获取课程完成率统计")
    @GetMapping("/course-completion-stats")
    public R<Map<String, Object>> getCourseCompletionStats() {
        try {
            Map<String, Object> stats = statisticsService.getCourseCompletionStats();
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取课程完成率统计失败", e);
            return R.error("获取课程完成率统计失败");
        }
    }

    /**
     * 获取用户权限分布统计
     */
    @ApiOperation("获取用户权限分布统计")
    @GetMapping("/user-access-distribution")
    public R<List<StatisticsDTO.AccessDistribution>> getUserAccessDistribution() {
        try {
            List<StatisticsDTO.AccessDistribution> distribution = 
                    statisticsService.getUserAccessDistribution();
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取用户权限分布统计失败", e);
            return R.error("获取用户权限分布统计失败");
        }
    }

    /**
     * 获取月度收入统计
     */
    @ApiOperation("获取月度收入统计")
    @GetMapping("/monthly-revenue")
    public R<List<StatisticsDTO.MonthlyRevenue>> getMonthlyRevenue() {
        try {
            List<StatisticsDTO.MonthlyRevenue> revenue = statisticsService.getMonthlyRevenue();
            return R.ok(revenue);
        } catch (Exception e) {
            log.error("获取月度收入统计失败", e);
            return R.error("获取月度收入统计失败");
        }
    }

    /**
     * 获取讲师课程统计
     */
    @ApiOperation("获取讲师课程统计")
    @GetMapping("/teacher-stats")
    public R<List<StatisticsDTO.TeacherStats>> getTeacherStats() {
        try {
            List<StatisticsDTO.TeacherStats> teacherStats = statisticsService.getTeacherStats();
            return R.ok(teacherStats);
        } catch (Exception e) {
            log.error("获取讲师课程统计失败", e);
            return R.error("获取讲师课程统计失败");
        }
    }

    /**
     * 获取仪表盘概览数据
     */
    @ApiOperation("获取仪表盘概览数据")
    @GetMapping("/dashboard-overview")
    public R<Map<String, Object>> getDashboardOverview() {
        try {
            Map<String, Object> overview = Map.of(
                    "totalUsers", statisticsService.getTotalUsers(),
                    "totalCourses", statisticsService.getTotalCourses(),
                    "todayNewUsers", statisticsService.getTodayNewUsers(),
                    "todaySales", statisticsService.getTodaySales(),
                    "activeUsers", statisticsService.getActiveUsers(),
                    "courseCompletionStats", statisticsService.getCourseCompletionStats()
            );
            return R.ok(overview);
        } catch (Exception e) {
            log.error("获取仪表盘概览数据失败", e);
            return R.error("获取仪表盘概览数据失败");
        }
    }
}
