package pox.com.dianfeng.service;

import pox.com.dianfeng.dto.StatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * 统计服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface IStatisticsService {

    /**
     * 获取总用户数
     * 
     * @return 总用户数
     */
    Long getTotalUsers();

    /**
     * 获取总课程数（上架状态）
     * 
     * @return 总课程数
     */
    Long getTotalCourses();

    /**
     * 获取今日新增用户数
     * 
     * @return 今日新增用户数
     */
    Long getTodayNewUsers();

    /**
     * 获取今日销售额
     * 
     * @return 今日销售额
     */
    Double getTodaySales();

    /**
     * 获取用户增长趋势数据
     * 
     * @param days 天数
     * @return 用户增长趋势数据
     */
    List<StatisticsDTO.TrendData> getUserGrowthTrend(Integer days);

    /**
     * 获取销售额趋势数据
     * 
     * @param days 天数
     * @return 销售额趋势数据
     */
    List<StatisticsDTO.SalesTrendData> getSalesTrend(Integer days);

    /**
     * 获取课程分类分布数据
     * 
     * @return 课程分类分布数据
     */
    List<StatisticsDTO.CategoryDistribution> getCoursesCategoryDistribution();

    /**
     * 获取热门课程排行
     * 
     * @param limit 限制数量
     * @return 热门课程列表
     */
    List<StatisticsDTO.HotCourse> getHotCourses(Integer limit);

    /**
     * 获取活跃用户数（最近7天有学习记录的用户）
     * 
     * @return 活跃用户数
     */
    Long getActiveUsers();

    /**
     * 获取课程完成率统计
     * 
     * @return 课程完成率数据
     */
    Map<String, Object> getCourseCompletionStats();

    /**
     * 获取用户权限分布统计
     * 
     * @return 用户权限分布数据
     */
    List<StatisticsDTO.AccessDistribution> getUserAccessDistribution();

    /**
     * 获取月度收入统计（最近12个月）
     * 
     * @return 月度收入数据
     */
    List<StatisticsDTO.MonthlyRevenue> getMonthlyRevenue();

    /**
     * 获取讲师课程统计
     * 
     * @return 讲师课程统计数据
     */
    List<StatisticsDTO.TeacherStats> getTeacherStats();
}
