package pox.com.dianfeng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pox.com.dianfeng.dto.StatisticsDTO;
import pox.com.dianfeng.entity.*;
import pox.com.dianfeng.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements IStatisticsService {

    @Autowired
    private IUsersService usersService;

    @Autowired
    private ICoursesService coursesService;

    @Autowired
    private IUserCourseAccessService userCourseAccessService;

    @Autowired
    private ITeachersService teachersService;

    @Autowired
    private ITagConfigsService tagConfigsService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM-dd");

    @Override
    public Long getTotalUsers() {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);
        return usersService.count(queryWrapper);
    }

    @Override
    public Long getTotalCourses() {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0).eq("status", 1);
        return coursesService.count(queryWrapper);
    }

    @Override
    public Long getTodayNewUsers() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .ge("created_at", startOfDay)
                .lt("created_at", endOfDay);
        
        return usersService.count(queryWrapper);
    }

    @Override
    public Double getTodaySales() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        
        QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .in("acquire_method", UserCourseAccess.AcquireMethod.PURCHASE)
                .ge("created_at", startOfDay)
                .lt("created_at", endOfDay);
        
        List<UserCourseAccess> todayAccess = userCourseAccessService.list(queryWrapper);
        
        return todayAccess.stream()
                .filter(access -> access.getPricePaid() != null)
                .mapToDouble(access -> access.getPricePaid().doubleValue())
                .sum();
    }

    @Override
    public List<StatisticsDTO.TrendData> getUserGrowthTrend(Integer days) {
        List<StatisticsDTO.TrendData> result = new ArrayList<>();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = startOfDay.plusDays(1);
            
            QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 0)
                    .ge("created_at", startOfDay)
                    .lt("created_at", endOfDay);
            
            Long count = usersService.count(queryWrapper);
            
            result.add(StatisticsDTO.TrendData.builder()
                    .date(date.format(DATE_FORMATTER))
                    .count(count)
                    .build());
        }
        
        return result;
    }

    @Override
    public List<StatisticsDTO.SalesTrendData> getSalesTrend(Integer days) {
        List<StatisticsDTO.SalesTrendData> result = new ArrayList<>();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = startOfDay.plusDays(1);
            
            QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 0)
                    .in("acquire_method", UserCourseAccess.AcquireMethod.PURCHASE)
                    .ge("created_at", startOfDay)
                    .lt("created_at", endOfDay);
            
            List<UserCourseAccess> dayAccess = userCourseAccessService.list(queryWrapper);
            
            Double amount = dayAccess.stream()
                    .filter(access -> access.getPricePaid() != null)
                    .mapToDouble(access -> access.getPricePaid().doubleValue())
                    .sum();
            
            result.add(StatisticsDTO.SalesTrendData.builder()
                    .date(date.format(DATE_FORMATTER))
                    .amount(amount)
                    .build());
        }
        
        return result;
    }

    @Override
    public List<StatisticsDTO.CategoryDistribution> getCoursesCategoryDistribution() {
        try {
            // 获取所有上架课程
            QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 0).eq("status", 1);
            List<Courses> courses = coursesService.list(queryWrapper);

            log.info("找到 {} 门上架课程", courses.size());

            // 获取所有标签配置
            List<TagConfigs> tagConfigs = tagConfigsService.list();
            Map<Integer, String> tagMap = tagConfigs.stream()
                    .filter(tag -> "course_category".equals(tag.getCategory()))
                    .collect(Collectors.toMap(TagConfigs::getValue, TagConfigs::getLabel));

            log.info("找到 {} 个课程分类标签", tagMap.size());

            // 如果没有课程，返回空列表
            if (courses.isEmpty()) {
                return new ArrayList<>();
            }

            // 统计分类分布
            Map<String, Long> categoryCount = courses.stream()
                    .collect(Collectors.groupingBy(
                            course -> {
                                String categoryName = tagMap.get(course.getCategoryId());
                                if (categoryName == null) {
                                    log.warn("课程 {} 的分类ID {} 未找到对应标签", course.getTitle(), course.getCategoryId());
                                    return "未分类";
                                }
                                return categoryName;
                            },
                            Collectors.counting()
                    ));

            log.info("分类统计结果: {}", categoryCount);

            return categoryCount.entrySet().stream()
                    .map(entry -> StatisticsDTO.CategoryDistribution.builder()
                            .categoryName(entry.getKey())
                            .count(entry.getValue())
                            .build())
                    .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取课程分类分布失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<StatisticsDTO.HotCourse> getHotCourses(Integer limit) {
        // 获取所有上架课程
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0).eq("status", 1);
        List<Courses> courses = coursesService.list(queryWrapper);
        
        // 获取讲师信息
        List<Teachers> teachers = teachersService.list();
        Map<Integer, String> teacherMap = teachers.stream()
                .collect(Collectors.toMap(Teachers::getId, Teachers::getName));
        
        // 获取标签配置
        List<TagConfigs> tagConfigs = tagConfigsService.list();
        Map<Integer, String> tagMap = tagConfigs.stream()
                .filter(tag -> "course_category".equals(tag.getCategory()))
                .collect(Collectors.toMap(TagConfigs::getValue, TagConfigs::getLabel));
        
        // 统计每个课程的销售数量
        QueryWrapper<UserCourseAccess> accessQuery = new QueryWrapper<>();
        accessQuery.eq("is_del", 0)
                .in("acquire_method", UserCourseAccess.AcquireMethod.PURCHASE)
                .eq("access_type", UserCourseAccess.AccessType.COURSE);

        List<UserCourseAccess> accessList = userCourseAccessService.list(accessQuery);
        final Map<Integer, Long> courseSalesMap = accessList.stream()
                .collect(Collectors.groupingBy(
                        UserCourseAccess::getCourseId,
                        Collectors.counting()
                ));

        // 构建热门课程列表
        List<StatisticsDTO.HotCourse> hotCourses = courses.stream()
                .map(course -> {
                    Long sales = courseSalesMap.getOrDefault(course.getId(), 0L);
                    return StatisticsDTO.HotCourse.builder()
                            .id(course.getId())
                            .title(course.getTitle())
                            .description(course.getDescription())
                            .coverImage(course.getCoverImage())
                            .teacherName(teacherMap.getOrDefault(course.getTeacherId(), "未知讲师"))
                            .categoryName(tagMap.getOrDefault(course.getCategoryId(), "未分类"))
                            .price(course.getPrice())
                            .sales(sales)
                            .studentCount(course.getStudentCount())
                            .rating(course.getRating())
                            .build();
                })
                .sorted((a, b) -> Long.compare(b.getSales(), a.getSales()))
                .limit(limit)
                .collect(Collectors.toList());
        
        return hotCourses;
    }

    @Override
    public Long getActiveUsers() {
        // 获取最近7天有权限记录的用户数量（作为活跃用户的近似值）
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        
        QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .ge("created_at", sevenDaysAgo)
                .select("DISTINCT user_id");
        
        return userCourseAccessService.count(queryWrapper);
    }

    @Override
    public Map<String, Object> getCourseCompletionStats() {
        // 这里简化处理，实际项目中需要根据学习进度表来计算
        Map<String, Object> stats = new HashMap<>();
        
        Long totalEnrollments = userCourseAccessService.count(
                new QueryWrapper<UserCourseAccess>().eq("is_del", 0));
        
        // 模拟完成率数据
        stats.put("totalEnrollments", totalEnrollments);
        stats.put("completedCount", Math.round(totalEnrollments * 0.65)); // 假设65%完成率
        stats.put("inProgressCount", Math.round(totalEnrollments * 0.25)); // 假设25%进行中
        stats.put("notStartedCount", Math.round(totalEnrollments * 0.10)); // 假设10%未开始
        stats.put("completionRate", 65.0);
        
        return stats;
    }

    @Override
    public List<StatisticsDTO.AccessDistribution> getUserAccessDistribution() {
        QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);
        List<UserCourseAccess> allAccess = userCourseAccessService.list(queryWrapper);
        
        long total = allAccess.size();
        if (total == 0) {
            return new ArrayList<>();
        }
        
        Map<String, Long> methodCount = allAccess.stream()
                .collect(Collectors.groupingBy(
                        access -> getAcquireMethodName(access.getAcquireMethod()),
                        Collectors.counting()
                ));
        
        return methodCount.entrySet().stream()
                .map(entry -> StatisticsDTO.AccessDistribution.builder()
                        .typeName(entry.getKey())
                        .count(entry.getValue())
                        .percentage(entry.getValue() * 100.0 / total)
                        .build())
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .collect(Collectors.toList());
    }

    @Override
    public List<StatisticsDTO.MonthlyRevenue> getMonthlyRevenue() {
        List<StatisticsDTO.MonthlyRevenue> result = new ArrayList<>();
        
        for (int i = 11; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusMonths(i);
            LocalDateTime startOfMonth = date.withDayOfMonth(1).atStartOfDay();
            LocalDateTime endOfMonth = startOfMonth.plusMonths(1);
            
            QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 0)
                    .in("acquire_method", UserCourseAccess.AcquireMethod.PURCHASE)
                    .ge("created_at", startOfMonth)
                    .lt("created_at", endOfMonth);
            
            List<UserCourseAccess> monthAccess = userCourseAccessService.list(queryWrapper);
            
            Double revenue = monthAccess.stream()
                    .filter(access -> access.getPricePaid() != null)
                    .mapToDouble(access -> access.getPricePaid().doubleValue())
                    .sum();
            
            result.add(StatisticsDTO.MonthlyRevenue.builder()
                    .month(date.format(DateTimeFormatter.ofPattern("yyyy-MM")))
                    .revenue(revenue)
                    .orderCount((long) monthAccess.size())
                    .build());
        }
        
        return result;
    }

    @Override
    public List<StatisticsDTO.TeacherStats> getTeacherStats() {
        List<Teachers> teachers = teachersService.list(
                new QueryWrapper<Teachers>().eq("is_del", 0).eq("status", 1));
        
        return teachers.stream()
                .map(teacher -> {
                    // 统计讲师课程数量
                    Long courseCount = coursesService.count(
                            new QueryWrapper<Courses>()
                                    .eq("is_del", 0)
                                    .eq("status", 1)
                                    .eq("teacher_id", teacher.getId()));
                    
                    // 统计学生总数
                    List<Courses> teacherCourses = coursesService.list(
                            new QueryWrapper<Courses>()
                                    .eq("is_del", 0)
                                    .eq("teacher_id", teacher.getId()));
                    
                    Long totalStudents = teacherCourses.stream()
                            .mapToLong(course -> course.getStudentCount() != null ? course.getStudentCount() : 0)
                            .sum();
                    
                    // 统计总收入（简化处理）
                    Double totalRevenue = 0.0;
                    if (!teacherCourses.isEmpty()) {
                        List<Integer> courseIds = teacherCourses.stream()
                                .map(Courses::getId)
                                .collect(Collectors.toList());

                        totalRevenue = userCourseAccessService.list(
                                new QueryWrapper<UserCourseAccess>()
                                        .eq("is_del", 0)
                                        .in("acquire_method", UserCourseAccess.AcquireMethod.PURCHASE)
                                        .in("course_id", courseIds))
                                .stream()
                                .filter(access -> access.getPricePaid() != null)
                                .mapToDouble(access -> access.getPricePaid().doubleValue())
                                .sum();
                    }
                    
                    // 计算平均评分
                    BigDecimal avgRating = teacherCourses.stream()
                            .filter(course -> course.getRating() != null)
                            .map(Courses::getRating)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(Math.max(1, teacherCourses.size())), 1, RoundingMode.HALF_UP);
                    
                    return StatisticsDTO.TeacherStats.builder()
                            .teacherId(teacher.getId())
                            .teacherName(teacher.getName())
                            .avatar(teacher.getAvatar())
                            .courseCount(courseCount)
                            .totalStudents(totalStudents)
                            .totalRevenue(totalRevenue)
                            .avgRating(avgRating)
                            .build();
                })
                .sorted((a, b) -> Double.compare(b.getTotalRevenue(), a.getTotalRevenue()))
                .collect(Collectors.toList());
    }

    /**
     * 获取获取方式名称
     */
    private String getAcquireMethodName(Integer method) {
        if (method == null) return "未知";
        
        switch (method) {
            case 1: return "购买";
            case 2: return "免费";
            case 3: return "积分兑换";
            case 4: return "优惠券兑换";
            case 5: return "管理员赠送";
            case 6: return "推广活动";
            default: return "未知";
        }
    }
}
