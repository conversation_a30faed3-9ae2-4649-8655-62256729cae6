package pox.com.dianfeng.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 统计数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
public class StatisticsDTO {

    /**
     * 趋势数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "TrendData", description = "趋势数据")
    public static class TrendData {
        @ApiModelProperty("日期")
        private String date;
        
        @ApiModelProperty("数量")
        private Long count;
    }

    /**
     * 销售趋势数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SalesTrendData", description = "销售趋势数据")
    public static class SalesTrendData {
        @ApiModelProperty("日期")
        private String date;
        
        @ApiModelProperty("销售额")
        private Double amount;
    }

    /**
     * 分类分布数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "CategoryDistribution", description = "分类分布数据")
    public static class CategoryDistribution {
        @ApiModelProperty("分类名称")
        private String categoryName;
        
        @ApiModelProperty("数量")
        private Long count;
    }

    /**
     * 热门课程数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "HotCourse", description = "热门课程数据")
    public static class HotCourse {
        @ApiModelProperty("课程ID")
        private Integer id;
        
        @ApiModelProperty("课程标题")
        private String title;
        
        @ApiModelProperty("课程描述")
        private String description;
        
        @ApiModelProperty("封面图片")
        private String coverImage;
        
        @ApiModelProperty("讲师名称")
        private String teacherName;
        
        @ApiModelProperty("分类名称")
        private String categoryName;
        
        @ApiModelProperty("价格")
        private BigDecimal price;
        
        @ApiModelProperty("销售数量")
        private Long sales;
        
        @ApiModelProperty("学习人数")
        private Integer studentCount;
        
        @ApiModelProperty("评分")
        private BigDecimal rating;
    }

    /**
     * 权限分布数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "AccessDistribution", description = "权限分布数据")
    public static class AccessDistribution {
        @ApiModelProperty("权限类型名称")
        private String typeName;
        
        @ApiModelProperty("数量")
        private Long count;
        
        @ApiModelProperty("百分比")
        private Double percentage;
    }

    /**
     * 月度收入数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "MonthlyRevenue", description = "月度收入数据")
    public static class MonthlyRevenue {
        @ApiModelProperty("年月")
        private String month;
        
        @ApiModelProperty("收入金额")
        private Double revenue;
        
        @ApiModelProperty("订单数量")
        private Long orderCount;
    }

    /**
     * 讲师统计数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "TeacherStats", description = "讲师统计数据")
    public static class TeacherStats {
        @ApiModelProperty("讲师ID")
        private Integer teacherId;
        
        @ApiModelProperty("讲师名称")
        private String teacherName;
        
        @ApiModelProperty("头像")
        private String avatar;
        
        @ApiModelProperty("课程数量")
        private Long courseCount;
        
        @ApiModelProperty("学生总数")
        private Long totalStudents;
        
        @ApiModelProperty("总收入")
        private Double totalRevenue;
        
        @ApiModelProperty("平均评分")
        private BigDecimal avgRating;
    }
}
