import request from "./request";

// 获取总用户数
export function getTotalUsers() {
  return request({
    url: "/statistics/total-users",
    method: "get",
  });
}

// 获取总课程数
export function getTotalCourses() {
  return request({
    url: "/statistics/total-courses",
    method: "get",
  });
}

// 获取今日新增用户数
export function getTodayNewUsers() {
  return request({
    url: "/statistics/today-new-users",
    method: "get",
  });
}

// 获取今日销售额
export function getTodaySales() {
  return request({
    url: "/statistics/today-sales",
    method: "get",
  });
}

// 获取用户增长趋势数据（按天）
export function getUserGrowthTrend(params) {
  return request({
    url: "/statistics/user-growth-trend",
    method: "get",
    params,
  });
}

// 获取销售额趋势数据（按天）
export function getSalesTrend(params) {
  return request({
    url: "/statistics/sales-trend",
    method: "get",
    params,
  });
}

// 获取课程分类分布数据
export function getCoursesCategoryDistribution() {
  return request({
    url: "/statistics/courses-category-distribution",
    method: "get",
  });
}

// 获取热门课程排行
export function getHotCourses(params) {
  return request({
    url: "/statistics/hot-courses",
    method: "get",
    params,
  });
}

// 获取活跃用户数
export function getActiveUsers() {
  return request({
    url: "/statistics/active-users",
    method: "get",
  });
}

// 获取课程完成率统计
export function getCourseCompletionStats() {
  return request({
    url: "/statistics/course-completion-stats",
    method: "get",
  });
}

// 获取用户权限分布统计
export function getUserAccessDistribution() {
  return request({
    url: "/statistics/user-access-distribution",
    method: "get",
  });
}

// 获取月度收入统计
export function getMonthlyRevenue() {
  return request({
    url: "/statistics/monthly-revenue",
    method: "get",
  });
}

// 获取讲师课程统计
export function getTeacherStats() {
  return request({
    url: "/statistics/teacher-stats",
    method: "get",
  });
}

// 获取仪表盘概览数据
export function getDashboardOverview() {
  return request({
    url: "/statistics/dashboard-overview",
    method: "get",
  });
}
