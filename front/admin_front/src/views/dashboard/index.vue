<template>
  <div class="page-container">
    <n-grid :cols="24" :x-gap="16" :y-gap="16">
      <!-- 统计卡片区域 -->
      <n-gi :span="6">
        <n-card class="stat-card" hoverable>
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <n-icon size="32"><PersonOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(statData.totalUsers) }}</div>
              <div class="stat-label">总用户数</div>
              <div class="stat-trend positive">
                <n-icon size="14"><TrendingUpOutline /></n-icon>
                <span>+{{ statData.todayNewUsers }} 今日新增</span>
              </div>
            </div>
          </div>
        </n-card>
      </n-gi>

      <n-gi :span="6">
        <n-card class="stat-card" hoverable>
          <div class="stat-content">
            <div class="stat-icon course-icon">
              <n-icon size="32"><BookOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(statData.totalCourses) }}</div>
              <div class="stat-label">总课程数</div>
              <div class="stat-trend">
                <n-icon size="14"><SchoolOutline /></n-icon>
                <span>{{ formatNumber(statData.activeUsers) }} 活跃学员</span>
              </div>
            </div>
          </div>
        </n-card>
      </n-gi>

      <n-gi :span="6">
        <n-card class="stat-card" hoverable>
          <div class="stat-content">
            <div class="stat-icon revenue-icon">
              <n-icon size="32"><CashOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ formatNumber(statData.todaySales) }}</div>
              <div class="stat-label">今日销售额</div>
              <div class="stat-trend positive">
                <n-icon size="14"><TrendingUpOutline /></n-icon>
                <span>较昨日 +12.5%</span>
              </div>
            </div>
          </div>
        </n-card>
      </n-gi>

      <n-gi :span="6">
        <n-card class="stat-card" hoverable>
          <div class="stat-content">
            <div class="stat-icon completion-icon">
              <n-icon size="32"><CheckmarkCircleOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statData.completionRate }}%</div>
              <div class="stat-label">课程完成率</div>
              <div class="stat-trend">
                <n-icon size="14"><BarChartOutline /></n-icon>
                <span>{{ formatNumber(statData.completedCount) }} 已完成</span>
              </div>
            </div>
          </div>
        </n-card>
      </n-gi>

      <!-- 图表分析区域 -->
      <n-gi :span="12">
        <n-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <n-icon size="18" color="#1890ff"><TrendingUpOutline /></n-icon>
              <span>用户增长趋势</span>
              <n-tag size="small" type="info">最近30天</n-tag>
            </div>
          </template>
          <n-spin :show="loadingUserTrend">
            <div ref="userChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <n-gi :span="12">
        <n-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <n-icon size="18" color="#36cbcb"><CashOutline /></n-icon>
              <span>销售额趋势</span>
              <n-tag size="small" type="success">最近30天</n-tag>
            </div>
          </template>
          <n-spin :show="loadingSalesTrend">
            <div ref="salesChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <n-gi :span="12">
        <n-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <n-icon size="18" color="#4ecb73"><PieChartOutline /></n-icon>
              <span>课程分类分布</span>
            </div>
          </template>
          <n-spin :show="loadingCategoryDistribution">
            <div ref="categoryChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <n-gi :span="12">
        <n-card class="table-card">
          <template #header>
            <div class="chart-header">
              <n-icon size="18" color="#f5222d"><FlameOutline /></n-icon>
              <span>热门课程排行</span>
              <n-tag size="small" type="warning">TOP 5</n-tag>
            </div>
          </template>
          <n-spin :show="loadingHotCourses">
            <div class="hot-courses-list">
              <div v-for="(course, index) in hotCourses" :key="course.id" class="hot-course-item">
                <div class="course-rank">{{ index + 1 }}</div>
                <div class="course-info">
                  <div class="course-title">{{ course.title }}</div>
                  <div class="course-meta">
                    <n-tag size="tiny" type="success">{{ course.categoryName }}</n-tag>
                    <span class="course-teacher">{{ course.teacherName }}</span>
                  </div>
                  <div class="course-stats">
                    <span class="course-price">¥{{ course.price }}</span>
                    <span class="course-sales">{{ course.sales }}人购买</span>
                  </div>
                </div>
              </div>
            </div>
          </n-spin>
        </n-card>
      </n-gi>

      <!-- 月度收入趋势 -->
      <n-gi :span="16">
        <n-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <n-icon size="18" color="#722ed1"><BarChartOutline /></n-icon>
              <span>月度收入趋势</span>
              <n-tag size="small" type="primary">最近12个月</n-tag>
            </div>
          </template>
          <n-spin :show="loadingMonthlyRevenue">
            <div ref="monthlyRevenueChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <!-- 讲师统计 -->
      <n-gi :span="8">
        <n-card class="table-card">
          <template #header>
            <div class="chart-header">
              <n-icon size="18" color="#13c2c2"><PeopleOutline /></n-icon>
              <span>优秀讲师</span>
              <n-tag size="small" type="info">TOP 5</n-tag>
            </div>
          </template>
          <n-spin :show="loadingTeacherStats">
            <div class="teacher-stats-list">
              <div v-for="(teacher, index) in teacherStats" :key="teacher.teacherId" class="teacher-item">
                <div class="teacher-rank">{{ index + 1 }}</div>
                <div class="teacher-info">
                  <div class="teacher-name">{{ teacher.teacherName }}</div>
                  <div class="teacher-meta">
                    <span class="teacher-courses">{{ teacher.courseCount }}门课程</span>
                    <span class="teacher-students">{{ formatNumber(teacher.totalStudents) }}学员</span>
                  </div>
                  <div class="teacher-revenue">¥{{ formatNumber(teacher.totalRevenue) }}</div>
                </div>
              </div>
            </div>
          </n-spin>
        </n-card>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { useMessage } from "naive-ui";
import * as echarts from "echarts";
import {
  PersonOutline,
  BookOutline,
  CashOutline,
  TrendingUpOutline,
  SchoolOutline,
  CheckmarkCircleOutline,
  BarChartOutline,
  PieChartOutline,
  FlameOutline,
  PeopleOutline,
} from "@vicons/ionicons5";
import {
  getTotalUsers,
  getTotalCourses,
  getTodayNewUsers,
  getTodaySales,
  getUserGrowthTrend,
  getSalesTrend,
  getCoursesCategoryDistribution,
  getHotCourses,
  getActiveUsers,
  getCourseCompletionStats,
  getMonthlyRevenue,
  getTeacherStats,
} from "@/api/statistics";

const message = useMessage();

// 图表DOM引用
const userChartRef = ref(null);
const salesChartRef = ref(null);
const categoryChartRef = ref(null);
const monthlyRevenueChartRef = ref(null);

// 图表实例
let userChart = null;
let salesChart = null;
let categoryChart = null;
let monthlyRevenueChart = null;

// 加载状态
const loadingUserTrend = ref(false);
const loadingSalesTrend = ref(false);
const loadingCategoryDistribution = ref(false);
const loadingMonthlyRevenue = ref(false);
const loadingHotCourses = ref(false);
const loadingTeacherStats = ref(false);

// 统计数据
const statData = reactive({
  totalUsers: 0,
  totalCourses: 0,
  todayNewUsers: 0,
  todaySales: 0,
  activeUsers: 0,
  completionRate: 0,
  completedCount: 0,
});

// 热门课程数据
const hotCourses = ref([]);

// 讲师统计数据
const teacherStats = ref([]);

// 格式化数字，添加千位分隔符
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};



// 初始化用户增长趋势图
const initUserChart = () => {
  if (userChart) {
    userChart.dispose();
  }
  userChart = echarts.init(userChartRef.value);
  userChart.setOption({
    tooltip: {
      trigger: "axis",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "新增用户",
        type: "line",
        smooth: true,
        data: [],
        itemStyle: {
          color: "#1890ff",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(24,144,255,0.3)",
            },
            {
              offset: 1,
              color: "rgba(24,144,255,0.1)",
            },
          ]),
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    userChart && userChart.resize();
  });
};

// 初始化销售额趋势图
const initSalesChart = () => {
  if (salesChart) {
    salesChart.dispose();
  }
  salesChart = echarts.init(salesChartRef.value);
  salesChart.setOption({
    tooltip: {
      trigger: "axis",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "¥{value}",
      },
    },
    series: [
      {
        name: "销售额",
        type: "line",
        smooth: true,
        data: [],
        itemStyle: {
          color: "#36cbcb",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(54,203,203,0.3)",
            },
            {
              offset: 1,
              color: "rgba(54,203,203,0.1)",
            },
          ]),
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    salesChart && salesChart.resize();
  });
};

// 初始化课程分类分布图
const initCategoryChart = () => {
  if (categoryChart) {
    categoryChart.dispose();
  }
  categoryChart = echarts.init(categoryChartRef.value);
  categoryChart.setOption({
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [
      {
        name: "课程分布",
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    categoryChart && categoryChart.resize();
  });
};

// 加载统计数据
const loadStatData = async () => {
  try {
    const [
      totalUsersRes,
      totalCoursesRes,
      todayNewUsersRes,
      todaySalesRes,
      activeUsersRes,
      completionStatsRes,
    ] = await Promise.all([
      getTotalUsers(),
      getTotalCourses(),
      getTodayNewUsers(),
      getTodaySales(),
      getActiveUsers(),
      getCourseCompletionStats(),
    ]);

    if (
      totalUsersRes.code === 200 &&
      totalCoursesRes.code === 200 &&
      todayNewUsersRes.code === 200 &&
      todaySalesRes.code === 200 &&
      activeUsersRes.code === 200 &&
      completionStatsRes.code === 200
    ) {
      statData.totalUsers = totalUsersRes.data;
      statData.totalCourses = totalCoursesRes.data;
      statData.todayNewUsers = todayNewUsersRes.data;
      statData.todaySales = todaySalesRes.data;
      statData.activeUsers = activeUsersRes.data;
      statData.completionRate = completionStatsRes.data.completionRate;
      statData.completedCount = completionStatsRes.data.completedCount;
    }
  } catch (error) {
    console.error("加载统计数据失败:", error);
    message.error("加载统计数据失败");
  }
};

// 加载用户增长趋势数据
const loadUserTrendData = async () => {
  loadingUserTrend.value = true;
  try {
    const params = {
      days: 30, // 最近30天
    };
    const res = await getUserGrowthTrend(params);
    if (res.code === 200) {
      const data = res.data || [];
      const dates = data.map((item) => item.date);
      const values = data.map((item) => item.count);

      userChart.setOption({
        xAxis: {
          data: dates,
        },
        series: [
          {
            data: values,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载用户增长趋势数据失败:", error);
    message.error("加载用户增长趋势数据失败");
  } finally {
    loadingUserTrend.value = false;
  }
};

// 加载销售额趋势数据
const loadSalesTrendData = async () => {
  loadingSalesTrend.value = true;
  try {
    const params = {
      days: 30, // 最近30天
    };
    const res = await getSalesTrend(params);
    if (res.code === 200) {
      const data = res.data || [];
      const dates = data.map((item) => item.date);
      const values = data.map((item) => item.amount);

      salesChart.setOption({
        xAxis: {
          data: dates,
        },
        series: [
          {
            data: values,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载销售额趋势数据失败:", error);
    message.error("加载销售额趋势数据失败");
  } finally {
    loadingSalesTrend.value = false;
  }
};

// 加载课程分类分布数据
const loadCategoryDistributionData = async () => {
  loadingCategoryDistribution.value = true;
  try {
    const res = await getCoursesCategoryDistribution();
    if (res.code === 200) {
      const data =
        res.data?.map((item) => ({
          value: item.count,
          name: item.categoryName,
        })) || [];

      categoryChart.setOption({
        series: [
          {
            data: data,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载课程分类分布数据失败:", error);
    message.error("加载课程分类分布数据失败");
  } finally {
    loadingCategoryDistribution.value = false;
  }
};

// 加载热门课程数据
const loadHotCoursesData = async () => {
  loadingHotCourses.value = true;
  try {
    const params = {
      limit: 5, // 获取前5条
    };
    const res = await getHotCourses(params);
    if (res.code === 200) {
      hotCourses.value = res.data || [];
    }
  } catch (error) {
    console.error("加载热门课程数据失败:", error);
    message.error("加载热门课程数据失败");
  } finally {
    loadingHotCourses.value = false;
  }
};



// 加载月度收入数据
const loadMonthlyRevenueData = async () => {
  loadingMonthlyRevenue.value = true;
  try {
    const res = await getMonthlyRevenue();
    if (res.code === 200) {
      const data = res.data || [];
      const months = data.map((item) => item.month);
      const revenues = data.map((item) => item.revenue);
      const orders = data.map((item) => item.orderCount);

      monthlyRevenueChart.setOption({
        xAxis: {
          data: months,
        },
        series: [
          {
            name: "收入",
            data: revenues,
          },
          {
            name: "订单数",
            data: orders,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载月度收入数据失败:", error);
    message.error("加载月度收入数据失败");
  } finally {
    loadingMonthlyRevenue.value = false;
  }
};

// 加载讲师统计数据
const loadTeacherStatsData = async () => {
  loadingTeacherStats.value = true;
  try {
    const res = await getTeacherStats();
    if (res.code === 200) {
      teacherStats.value = (res.data || []).slice(0, 5); // 只取前5名
    }
  } catch (error) {
    console.error("加载讲师统计数据失败:", error);
    message.error("加载讲师统计数据失败");
  } finally {
    loadingTeacherStats.value = false;
  }
};



// 初始化月度收入图
const initMonthlyRevenueChart = () => {
  if (monthlyRevenueChart) {
    monthlyRevenueChart.dispose();
  }
  monthlyRevenueChart = echarts.init(monthlyRevenueChartRef.value);
  monthlyRevenueChart.setOption({
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    legend: {
      data: ["收入", "订单数"],
    },
    xAxis: [
      {
        type: "category",
        data: [],
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "收入",
        position: "left",
        axisLabel: {
          formatter: "¥{value}",
        },
      },
      {
        type: "value",
        name: "订单数",
        position: "right",
        axisLabel: {
          formatter: "{value}",
        },
      },
    ],
    series: [
      {
        name: "收入",
        type: "bar",
        yAxisIndex: 0,
        data: [],
        itemStyle: {
          color: "#1890ff",
        },
      },
      {
        name: "订单数",
        type: "line",
        yAxisIndex: 1,
        data: [],
        itemStyle: {
          color: "#52c41a",
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    monthlyRevenueChart && monthlyRevenueChart.resize();
  });
};

onMounted(() => {
  // 初始化图表
  initUserChart();
  initSalesChart();
  initCategoryChart();
  initMonthlyRevenueChart();

  // 加载数据
  loadStatData();
  loadUserTrendData();
  loadSalesTrendData();
  loadCategoryDistributionData();
  loadMonthlyRevenueData();
  loadHotCoursesData();
  loadTeacherStatsData();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.stat-card {
  height: 140px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 8px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 16px;
  margin-right: 16px;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.course-icon {
  background: linear-gradient(135deg, #36cbcb, #5cdbd3);
}

.revenue-icon {
  background: linear-gradient(135deg, #fbd437, #ffec3d);
}

.completion-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;

  &.positive {
    color: #52c41a;
  }
}

.chart-card,
.table-card {
  height: 380px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1a1a1a;
}

.chart-container {
  height: 320px;
}

.hot-courses-list {
  height: 320px;
  overflow-y: auto;
}

.hot-course-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.course-rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 12px;
}

.course-info {
  flex: 1;
}

.course-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.course-teacher {
  font-size: 12px;
  color: #666;
}

.course-stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

.course-price {
  color: #f5222d;
  font-weight: bold;
  font-size: 14px;
}

.course-sales {
  font-size: 12px;
  color: #666;
}

.teacher-stats-list {
  height: 320px;
  overflow-y: auto;
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.teacher-rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #13c2c2, #36cbcb);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 12px;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  font-size: 14px;
}

.teacher-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.teacher-revenue {
  color: #f5222d;
  font-weight: bold;
  font-size: 14px;
}

// 响应式设计
@media (max-width: 1200px) {
  .stat-card {
    height: 120px;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
  }

  .stat-value {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .chart-card,
  .table-card {
    height: 300px;
  }

  .chart-container {
    height: 240px;
  }
}
</style>
